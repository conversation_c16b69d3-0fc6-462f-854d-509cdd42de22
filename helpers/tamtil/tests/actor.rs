//! # TAMTIL Blackbox Tests: Production-Ready Validation
//!
//! ## Testing Philosophy
//!
//! These tests treat Platform, Context, and Actor as complete blackboxes,
//! only testing through the public API with no mocks, fakes, or stubs.
//! All implementations are real and production-ready.
//!
//! ### Test Coverage Strategy
//! - **Functional Testing**: All core functionality works as specified
//! - **Error Handling**: Graceful failure modes and recovery
//! - **Concurrency**: Multiple actors and concurrent operations
//! - **State Management**: Event sourcing and memory operations
//! - **Type Safety**: Compile-time and runtime type guarantees
//! - **Performance**: Reasonable performance characteristics
//!
//! ### No Mocks Policy
//! Every test uses real implementations:
//! - Real actors with real business logic
//! - Real serialization with rkyv
//! - Real memory operations and event sourcing
//! - Real error conditions and recovery

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};

// ============================================================================
// TEST DOMAIN: CALCULATOR ACTIONS AND REACTIONS
// ============================================================================

/// ## Real Calculator Action for Testing
///
/// This is a complete, production-ready action implementation that demonstrates
/// all aspects of TAMTIL's action-reaction pattern.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct CalculatorAction {
    operation: String,
    value: i32,
}

/// ## Real Calculator Reaction for Testing
///
/// This reaction implements proper event sourcing with memory operations,
/// demonstrating TAMTIL's state management capabilities.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct CalculatorReaction {
    result: i32,
    message: String,
}

impl Reaction for CalculatorReaction {
    /// ## Event Sourcing Implementation
    ///
    /// This method demonstrates how reactions describe state changes
    /// through atomic memory operations.
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Update {
                key: "current_value".to_string(),
                value: MemoryValue::new(&self.result).unwrap(),
            },
            MemoryOperation::Create {
                key: format!("operation_log:{}",
                    std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_millis()
                ),
                value: MemoryValue::new(&self.message).unwrap(),
            },
        ]
    }
}

impl Action for CalculatorAction {
    type Reaction = CalculatorReaction;

    /// ## Business Logic Implementation
    ///
    /// This demonstrates real business logic with state access,
    /// error handling, and reaction generation.
    async fn act(&self, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
        // Read current state from memories
        let current: i32 = memories.recall("current_value").await?.unwrap_or(0);

        // Perform business logic based on operation
        let (result, message) = match self.operation.as_str() {
            "add" => {
                let new_value = current + self.value;
                (new_value, format!("Added {} to {} = {}", self.value, current, new_value))
            }
            "subtract" => {
                let new_value = current - self.value;
                (new_value, format!("Subtracted {} from {} = {}", self.value, current, new_value))
            }
            "multiply" => {
                let new_value = current * self.value;
                (new_value, format!("Multiplied {} by {} = {}", current, self.value, new_value))
            }
            "divide" => {
                if self.value == 0 {
                    (current, "Cannot divide by zero".to_string())
                } else {
                    let new_value = current / self.value;
                    (new_value, format!("Divided {} by {} = {}", current, self.value, new_value))
                }
            }
            "reset" => {
                (0, "Reset calculator to zero".to_string())
            }
            _ => {
                (current, format!("Unknown operation: {}", self.operation))
            }
        };

        Ok(CalculatorReaction { result, message })
    }
}

/// Test 1: Platform creation and basic functionality
#[tokio::test]
async fn test_platform_creation() {
    let platform = Platform::new("test_platform").await.unwrap();
    
    // Platform should be created successfully
    assert_eq!(platform.id().as_str(), "test_platform");
    
    // Platform should start with no contexts
    let stats = platform.stats().await;
    assert_eq!(stats.total_contexts, 0);
    assert_eq!(stats.total_actors, 0);
}

/// Test 2: Context creation and management
#[tokio::test]
async fn test_context_creation() {
    let platform = Platform::new("test_platform").await.unwrap();
    
    // Create multiple contexts
    let context1 = platform.create_context("context_1").await.unwrap();
    let context2 = platform.create_context("context_2").await.unwrap();
    
    // Contexts should have correct IDs
    assert_eq!(context1.id().as_str(), "context_1");
    assert_eq!(context2.id().as_str(), "context_2");
    
    // Platform should track contexts
    let stats = platform.stats().await;
    assert_eq!(stats.total_contexts, 2);
    assert_eq!(stats.total_actors, 0);
}

/// Test 3: Actor spawning and basic operations
#[tokio::test]
async fn test_actor_spawning() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();
    
    // Spawn a calculator actor
    let actor_id = ActorId::new("calculator_1");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();
    
    // Context should track the actor
    let stats = context.stats().await;
    assert_eq!(stats.actor_count, 1);
    assert!(stats.actor_ids.contains(&actor_id));
    
    // Platform should track the actor
    let platform_stats = platform.stats().await;
    assert_eq!(platform_stats.total_actors, 1);
}

/// Test 4: Real actor computation with state persistence
#[tokio::test]
async fn test_actor_computation() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();
    
    let actor_id = ActorId::new("calculator");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();
    
    let proxy = context.actor(&actor_id);
    
    // Test addition
    let action = CalculatorAction {
        operation: "add".to_string(),
        value: 10,
    };
    
    let response = proxy.act(action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 10);
    assert!(reaction.message.contains("Added 10"));
    
    // Test multiplication (should use previous result)
    let action = CalculatorAction {
        operation: "multiply".to_string(),
        value: 3,
    };
    
    let response = proxy.act(action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 30); // 10 * 3
    assert!(reaction.message.contains("Multiplied 10 by 3"));

    // Test subtraction
    let action = CalculatorAction {
        operation: "subtract".to_string(),
        value: 5,
    };

    let response = proxy.act(action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 25); // 30 - 5
    assert!(reaction.message.contains("Subtracted 5 from 30"));
}

/// Test 5: Error handling and edge cases
#[tokio::test]
async fn test_error_handling() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();
    
    let actor_id = ActorId::new("calculator");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();
    
    let proxy = context.actor(&actor_id);
    
    // Test division by zero
    let action = CalculatorAction {
        operation: "divide".to_string(),
        value: 0,
    };
    
    let response = proxy.act(action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 0); // Should remain unchanged
    assert!(reaction.message.contains("Cannot divide by zero"));

    // Test unknown operation
    let action = CalculatorAction {
        operation: "unknown".to_string(),
        value: 42,
    };

    let response = proxy.act(action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 0); // Should remain unchanged
    assert!(reaction.message.contains("Unknown operation"));
}

/// Test 6: Multiple actors with isolated state
#[tokio::test]
async fn test_multiple_actors() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();
    
    // Spawn two calculator actors
    let actor1_id = ActorId::new("calculator_1");
    let actor2_id = ActorId::new("calculator_2");
    
    let actor1 = ActorBuilder::new(actor1_id.clone()).await.unwrap().build::<CalculatorAction>();
    let actor2 = ActorBuilder::new(actor2_id.clone()).await.unwrap().build::<CalculatorAction>();
    
    context.spawn(actor1_id.clone(), actor1).await.unwrap();
    context.spawn(actor2_id.clone(), actor2).await.unwrap();
    
    let proxy1 = context.actor(&actor1_id);
    let proxy2 = context.actor(&actor2_id);
    
    // Set different values for each actor
    let action1 = CalculatorAction {
        operation: "add".to_string(),
        value: 100,
    };
    
    let action2 = CalculatorAction {
        operation: "add".to_string(),
        value: 200,
    };
    
    let response1 = proxy1.act(action1).await.unwrap();
    let response2 = proxy2.act(action2).await.unwrap();
    
    let reaction1: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response1).unwrap();
    let reaction2: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response2).unwrap();
    
    // Each actor should maintain separate state
    assert_eq!(reaction1.result, 100);
    assert_eq!(reaction2.result, 200);
    
    // Verify state isolation continues
    let multiply_action = CalculatorAction {
        operation: "multiply".to_string(),
        value: 2,
    };
    
    let response1 = proxy1.act(multiply_action.clone()).await.unwrap();
    let reaction1: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response1).unwrap();
    assert_eq!(reaction1.result, 200); // 100 * 2

    let response2 = proxy2.act(multiply_action).await.unwrap();
    let reaction2: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response2).unwrap();
    assert_eq!(reaction2.result, 400); // 200 * 2
}

/// Test 7: Reset functionality and state management
#[tokio::test]
async fn test_reset_functionality() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();
    
    let actor_id = ActorId::new("calculator");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();
    
    let proxy = context.actor(&actor_id);
    
    // Build up some state
    let actions = vec![
        CalculatorAction { operation: "add".to_string(), value: 50 },
        CalculatorAction { operation: "multiply".to_string(), value: 4 },
        CalculatorAction { operation: "subtract".to_string(), value: 25 },
    ];
    
    for action in actions {
        proxy.act(action).await.unwrap();
    }
    
    // Reset the calculator
    let reset_action = CalculatorAction {
        operation: "reset".to_string(),
        value: 999, // Value should be ignored
    };
    
    let response = proxy.act(reset_action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 0);
    assert!(reaction.message.contains("Reset"));

    // Verify next operation starts from zero
    let add_action = CalculatorAction {
        operation: "add".to_string(),
        value: 42,
    };

    let response = proxy.act(add_action).await.unwrap();
    let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
    assert_eq!(reaction.result, 42); // Should start from 0, not previous state
}

/// Test 8: Complex workflow with multiple operations
#[tokio::test]
async fn test_complex_workflow() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();

    let actor_id = ActorId::new("calculator");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();

    let proxy = context.actor(&actor_id);

    // Complex calculation: ((10 + 5) * 3) - 7 = 38
    let operations = vec![
        ("add", 10, 10),
        ("add", 5, 15),
        ("multiply", 3, 45),
        ("subtract", 7, 38),
    ];

    for (operation, value, expected) in operations {
        let action = CalculatorAction {
            operation: operation.to_string(),
            value,
        };

        let response = proxy.act(action).await.unwrap();
        let reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&response).unwrap();
        assert_eq!(reaction.result, expected,
                  "Operation {} with value {} failed: expected {}, got {}",
                  operation, value, expected, reaction.result);
    }
}

/// Test 9: Concurrent access to single actor
#[tokio::test]
async fn test_concurrent_access() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();

    let actor_id = ActorId::new("calculator");
    let actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    context.spawn(actor_id.clone(), actor).await.unwrap();

    // Spawn multiple concurrent tasks
    let mut handles = Vec::new();

    for i in 1..=10 {
        let context_clone = context.clone();
        let actor_id_clone = actor_id.clone();

        let handle = tokio::spawn(async move {
            let proxy = context_clone.actor(&actor_id_clone);
            let action = CalculatorAction {
                operation: "add".to_string(),
                value: i,
            };

            proxy.act(action).await
        });

        handles.push(handle);
    }

    // Wait for all operations to complete
    let mut results = Vec::new();
    for handle in handles {
        let result = handle.await.unwrap();
        assert!(result.is_ok(), "Concurrent operation should succeed");
        results.push(result.unwrap());
    }

    // All operations should complete successfully
    assert_eq!(results.len(), 10);
    for result in results {
        assert!(!result.is_empty(), "Result should not be empty");
    }
}

/// Test 10: Multiple contexts with isolated actors
#[tokio::test]
async fn test_multiple_contexts() {
    let platform = Platform::new("test_platform").await.unwrap();

    // Create multiple contexts
    let context1 = platform.create_context("math_context").await.unwrap();
    let context2 = platform.create_context("science_context").await.unwrap();

    // Spawn actors in different contexts
    let math_actor_id = ActorId::new("math_calculator");
    let science_actor_id = ActorId::new("science_calculator");

    let math_actor = ActorBuilder::new(math_actor_id.clone()).await.unwrap().build::<CalculatorAction>();
    let science_actor = ActorBuilder::new(science_actor_id.clone()).await.unwrap().build::<CalculatorAction>();

    context1.spawn(math_actor_id.clone(), math_actor).await.unwrap();
    context2.spawn(science_actor_id.clone(), science_actor).await.unwrap();

    // Each context should track its own actors
    let stats1 = context1.stats().await;
    let stats2 = context2.stats().await;

    assert_eq!(stats1.actor_count, 1);
    assert_eq!(stats2.actor_count, 1);
    assert!(stats1.actor_ids.contains(&math_actor_id));
    assert!(stats2.actor_ids.contains(&science_actor_id));

    // Platform should track all actors
    let platform_stats = platform.stats().await;
    assert_eq!(platform_stats.total_contexts, 2);
    assert_eq!(platform_stats.total_actors, 2);

    // Actors in different contexts should have isolated state
    let math_proxy = context1.actor(&math_actor_id);
    let science_proxy = context2.actor(&science_actor_id);

    let math_action = CalculatorAction {
        operation: "add".to_string(),
        value: 100,
    };

    let science_action = CalculatorAction {
        operation: "add".to_string(),
        value: 500,
    };

    let math_response = math_proxy.act(math_action).await.unwrap();
    let science_response = science_proxy.act(science_action).await.unwrap();

    let math_reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&math_response).unwrap();
    let science_reaction: CalculatorReaction = rkyv::api::high::from_bytes::<CalculatorReaction, rkyv::rancor::Error>(&science_response).unwrap();

    assert_eq!(math_reaction.result, 100);
    assert_eq!(science_reaction.result, 500);
}

/// Test 11: Actor not found error handling
#[tokio::test]
async fn test_actor_not_found() {
    let platform = Platform::new("test_platform").await.unwrap();
    let context = platform.create_context("test_context").await.unwrap();

    let nonexistent_id = ActorId::new("nonexistent_calculator");
    let proxy = context.actor(&nonexistent_id);

    let action = CalculatorAction {
        operation: "add".to_string(),
        value: 42,
    };

    let result = proxy.act(action).await;
    assert!(result.is_err(), "Should fail for nonexistent actor");

    match result.unwrap_err() {
        TamtilError::ActorNotFound { id } => {
            assert_eq!(id, "nonexistent_calculator");
        }
        _ => panic!("Expected ActorNotFound error"),
    }
}

/// Test 12: Hierarchical actor IDs
#[tokio::test]
async fn test_hierarchical_actor_ids() {
    // Test hierarchical relationships
    let root_id = ActorId::new("department");
    let team_id = ActorId::new("department/team");
    let member_id = ActorId::new("department/team/member");

    // Test parent relationships
    assert_eq!(team_id.parent(), Some(root_id.clone()));
    assert_eq!(member_id.parent(), Some(team_id.clone()));
    assert_eq!(root_id.parent(), None);

    // Test depth calculation
    assert_eq!(root_id.depth(), 0);
    assert_eq!(team_id.depth(), 1);
    assert_eq!(member_id.depth(), 2);

    // Test child relationships
    assert!(team_id.is_child_of(&root_id));
    assert!(member_id.is_child_of(&team_id));
    assert!(member_id.is_child_of(&root_id));
    assert!(!root_id.is_child_of(&team_id));
}

/// Test 13: Memory value serialization
#[tokio::test]
async fn test_memory_value_serialization() {
    // Test different data types
    let string_val = MemoryValue::string("test string").unwrap();
    let number_val = MemoryValue::number(42.5).unwrap();
    let boolean_val = MemoryValue::boolean(true).unwrap();

    // All should serialize successfully
    assert!(!string_val.bytes.is_empty());
    assert!(!number_val.bytes.is_empty());
    assert!(!boolean_val.bytes.is_empty());

    // Test edge cases
    let empty_string = MemoryValue::string("").unwrap();
    let zero_number = MemoryValue::number(0.0).unwrap();
    let false_boolean = MemoryValue::boolean(false).unwrap();
    let negative_number = MemoryValue::number(-123.456).unwrap();

    assert!(!empty_string.bytes.is_empty());
    assert!(!zero_number.bytes.is_empty());
    assert!(!false_boolean.bytes.is_empty());
    assert!(!negative_number.bytes.is_empty());
}
