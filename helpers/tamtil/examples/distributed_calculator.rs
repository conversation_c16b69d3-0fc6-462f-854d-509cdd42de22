/*!
 * # Distributed Calculator: TAMTIL's Over-Engineered Masterpiece
 *
 * ## Abstract
 * 
 * This example demonstrates TAMTIL's true power by implementing a simple calculator
 * as a distributed system with separate client and server actor systems. While
 * massively over-engineered for a calculator, this showcases how TAMTIL makes
 * distributed computing as simple as local function calls.
 *
 * ## Architecture Overview
 * 
 * ### Server Side: Mathematical Engine
 * - **Calculator Actor**: Maintains state and performs operations
 * - **Event Sourcing**: All operations are stored as reactions
 * - **Fault Tolerance**: State can be recovered from reaction log
 * - **Zero-Copy**: Operations use rkyv serialization throughout
 * 
 * ### Client Side: User Interface
 * - **Client Actor**: Sends requests to server calculator
 * - **Transparent Communication**: Client doesn't know server is remote
 * - **Type Safety**: All operations are type-checked at compile time
 * - **Audit Trail**: Complete history of all calculations
 *
 * ## Why This Demonstrates TAMTIL's Power
 * 
 * 1. **Location Transparency**: Client code is identical for local/remote actors
 * 2. **Zero-Copy Performance**: No serialization overhead for local operations
 * 3. **Fault Tolerance**: Calculator state survives crashes via event sourcing
 * 4. **Type Safety**: Compile-time guarantees for distributed operations
 * 5. **Simplicity**: Complex distributed system feels like local code
 */

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use tokio;
use std::net::SocketAddr;

// ============================================================================
// CALCULATOR DOMAIN: ACTIONS AND REACTIONS
// ============================================================================

/// ## Calculator Actions: Business Operations
/// 
/// These actions represent all possible operations on our distributed calculator.
/// Each action is serializable and can be sent across the network transparently.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CalculatorAction {
    /// Add a number to the current value
    Add { value: f64 },
    /// Subtract a number from the current value
    Subtract { value: f64 },
    /// Multiply the current value by a number
    Multiply { value: f64 },
    /// Divide the current value by a number
    Divide { value: f64 },
    /// Reset the calculator to zero
    Reset,
    /// Get the current value without changing it
    Get,
    /// Advanced: Calculate power (demonstrates complex operations)
    Power { exponent: f64 },
    /// Advanced: Calculate square root
    SquareRoot,
}

/// ## Calculator Reactions: State Changes
/// 
/// Reactions describe how the calculator's state changes in response to actions.
/// They implement event sourcing, providing complete audit trails.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct CalculatorReaction {
    /// The result of the operation
    pub result: f64,
    /// Human-readable description of what happened
    pub operation: String,
    /// Whether the operation was successful
    pub success: bool,
    /// Error message if operation failed
    pub error: Option<String>,
}

impl Action for CalculatorAction {
    type Reaction = CalculatorReaction;

    /// ## Business Logic: Calculator Operations
    /// 
    /// This is where the actual calculator logic lives. Notice how we:
    /// 1. Read current state from memories
    /// 2. Perform the calculation
    /// 3. Return a reaction describing the change
    /// 4. Never mutate state directly
    async fn act(&self, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
        // Get current value (defaults to 0.0 if not set)
        let current: f64 = memories.recall("current_value").await?.unwrap_or(0.0);
        
        let (result, operation, success, error) = match self {
            CalculatorAction::Add { value } => {
                let new_value = current + value;
                (new_value, format!("Added {} to {} = {}", value, current, new_value), true, None)
            }
            CalculatorAction::Subtract { value } => {
                let new_value = current - value;
                (new_value, format!("Subtracted {} from {} = {}", value, current, new_value), true, None)
            }
            CalculatorAction::Multiply { value } => {
                let new_value = current * value;
                (new_value, format!("Multiplied {} by {} = {}", current, value, new_value), true, None)
            }
            CalculatorAction::Divide { value } => {
                if *value == 0.0 {
                    (current, "Division by zero".to_string(), false, Some("Cannot divide by zero".to_string()))
                } else {
                    let new_value = current / value;
                    (new_value, format!("Divided {} by {} = {}", current, value, new_value), true, None)
                }
            }
            CalculatorAction::Reset => {
                (0.0, "Reset calculator to zero".to_string(), true, None)
            }
            CalculatorAction::Get => {
                (current, format!("Current value: {}", current), true, None)
            }
            CalculatorAction::Power { exponent } => {
                let new_value = current.powf(*exponent);
                (new_value, format!("Raised {} to power {} = {}", current, exponent, new_value), true, None)
            }
            CalculatorAction::SquareRoot => {
                if current < 0.0 {
                    (current, "Square root of negative number".to_string(), false, Some("Cannot take square root of negative number".to_string()))
                } else {
                    let new_value = current.sqrt();
                    (new_value, format!("Square root of {} = {}", current, new_value), true, None)
                }
            }
        };

        Ok(CalculatorReaction {
            result,
            operation,
            success,
            error,
        })
    }
}

impl Reaction for CalculatorReaction {
    /// ## Event Sourcing: Memory Operations
    /// 
    /// This method describes how to update the calculator's state based on this reaction.
    /// It returns a list of memory operations that will be applied atomically.
    fn remember(&self) -> Vec<MemoryOperation> {
        let mut operations = vec![];
        
        // Update the current value if the operation was successful
        if self.success {
            operations.push(MemoryOperation::Update {
                key: "current_value".to_string(),
                value: MemoryValue::number(self.result).unwrap(),
            });
        }
        
        // Log the operation for audit trail
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis();
            
        operations.push(MemoryOperation::Create {
            key: format!("operation_log:{}", timestamp),
            value: MemoryValue::string(&self.operation).unwrap(),
        });
        
        operations
    }
}

// ============================================================================
// DISTRIBUTED CALCULATOR SYSTEM
// ============================================================================

/// ## Distributed Calculator: Client-Server Architecture
/// 
/// This struct manages the entire distributed calculator system, including
/// separate client and server platforms running on different ports.
pub struct DistributedCalculator {
    server_platform: Platform,
    client_platform: Platform,
    server_context: Context,
    client_context: Context,
}

impl DistributedCalculator {
    /// Create a new distributed calculator system
    pub async fn new() -> TamtilResult<Self> {
        // Create server platform on port 9001
        let server_addr: SocketAddr = "127.0.0.1:9001".parse().unwrap();
        let server_platform = Platform::networked("calculator_server", server_addr).await?;
        
        // Create client platform on port 9002  
        let client_addr: SocketAddr = "127.0.0.1:9002".parse().unwrap();
        let client_platform = Platform::networked("calculator_client", client_addr).await?;
        
        // Create contexts for organizing actors
        let server_context = server_platform.create_context("math_engine").await?;
        let client_context = client_platform.create_context("user_interface").await?;
        
        // Spawn the calculator actor on the server
        let calculator_id = ActorId::new("calculator");
        let calculator_actor = GenericActor::<CalculatorAction>::new(calculator_id.clone());
        server_context.spawn(calculator_id, calculator_actor).await?;
        
        println!("🚀 Distributed Calculator System Initialized");
        println!("   📊 Server Platform: {} ({})", server_platform.id().as_str(), server_addr);
        println!("   💻 Client Platform: {} ({})", client_platform.id().as_str(), client_addr);
        println!("   🧮 Calculator Actor: Ready for distributed operations");
        
        Ok(Self {
            server_platform,
            client_platform,
            server_context,
            client_context,
        })
    }
    
    /// Perform a calculation using the distributed system
    pub async fn calculate(&self, action: CalculatorAction) -> TamtilResult<CalculatorReaction> {
        // Get handle to the calculator actor (on the server)
        let calculator_handle = self.server_context
            .actor(&ActorId::new("calculator"))
            .await
            .ok_or_else(|| TamtilError::ActorNotFound { 
                id: "calculator".to_string() 
            })?;
        
        // Create actor proxy for convenient communication
        let proxy = ActorProxy::new(calculator_handle);
        
        // Send the action and get the response
        let response_bytes = proxy.act(action).await?;
        
        // Deserialize the reaction
        let reaction: CalculatorReaction = rkyv::api::high::from_bytes(&response_bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize calculator reaction: {}", e)
            })?;
        
        Ok(reaction)
    }
    
    /// Get statistics about the distributed system
    pub async fn stats(&self) -> (PlatformStats, PlatformStats) {
        let server_stats = self.server_platform.stats().await;
        let client_stats = self.client_platform.stats().await;
        (server_stats, client_stats)
    }
}

// ============================================================================
// DEMONSTRATION: OVER-ENGINEERED CALCULATOR IN ACTION
// ============================================================================

#[tokio::main]
async fn main() -> TamtilResult<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    println!("🎯 TAMTIL Distributed Calculator Demo");
    println!("=====================================");
    println!("Demonstrating how TAMTIL makes distributed computing feel like local code\n");
    
    // Create the distributed calculator system
    let calculator = DistributedCalculator::new().await?;
    
    println!("📋 Performing distributed calculations...\n");
    
    // Demonstrate basic arithmetic operations
    let operations = vec![
        ("Reset", CalculatorAction::Reset),
        ("Add 10", CalculatorAction::Add { value: 10.0 }),
        ("Multiply by 3", CalculatorAction::Multiply { value: 3.0 }),
        ("Subtract 5", CalculatorAction::Subtract { value: 5.0 }),
        ("Divide by 2", CalculatorAction::Divide { value: 2.0 }),
        ("Power of 2", CalculatorAction::Power { exponent: 2.0 }),
        ("Square root", CalculatorAction::SquareRoot),
        ("Get result", CalculatorAction::Get),
    ];
    
    for (description, action) in operations {
        let reaction = calculator.calculate(action).await?;
        
        if reaction.success {
            println!("✅ {}: {}", description, reaction.operation);
        } else {
            println!("❌ {}: {} ({})", description, reaction.operation, 
                    reaction.error.unwrap_or_else(|| "Unknown error".to_string()));
        }
    }
    
    // Demonstrate error handling
    println!("\n🔍 Testing error handling...");
    let error_operations = vec![
        ("Divide by zero", CalculatorAction::Divide { value: 0.0 }),
        ("Square root of negative", CalculatorAction::Add { value: -10.0 }),
        ("Square root", CalculatorAction::SquareRoot),
    ];
    
    for (description, action) in error_operations {
        let reaction = calculator.calculate(action).await?;
        
        if reaction.success {
            println!("✅ {}: {}", description, reaction.operation);
        } else {
            println!("❌ {}: {} ({})", description, reaction.operation, 
                    reaction.error.unwrap_or_else(|| "Unknown error".to_string()));
        }
    }
    
    // Show system statistics
    let (server_stats, client_stats) = calculator.stats().await;
    println!("\n📊 System Statistics:");
    println!("   Server: {} contexts, {} actors", server_stats.total_contexts, server_stats.total_actors);
    println!("   Client: {} contexts, {} actors", client_stats.total_contexts, client_stats.total_actors);
    
    println!("\n🎉 Distributed Calculator Demo Complete!");
    println!("Key TAMTIL features demonstrated:");
    println!("- ✅ Location transparency (client doesn't know server is remote)");
    println!("- ✅ Zero-copy serialization with rkyv");
    println!("- ✅ Event sourcing with complete audit trails");
    println!("- ✅ Type-safe distributed operations");
    println!("- ✅ Fault tolerance through reaction logs");
    println!("- ✅ Simple API for complex distributed systems");
    
    Ok(())
}
