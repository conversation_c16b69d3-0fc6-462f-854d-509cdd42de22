[package]
name = "jawad"
version = "0.1.0"
edition = "2024"
description = "Jawad - A distributed, fault-tolerant, sarcastic Moroccan Darija chat AI built with TAMTIL"
authors = ["TAMTIL Team"]

[dependencies]
# TAMTIL - Our distributed actor system
tamtil = { path = ".." }

# HTTP client for Gemini API
reqwest = { version = "0.12", features = ["json", "rustls-tls"] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization (rkyv only - no serde!)
rkyv = { version = "0.8", features = ["validation"] }

# JSON for Gemini API (only for API communication)
serde_json = "1.0"

# CLI interface
clap = { version = "4.0", features = ["derive"] }
crossterm = "0.28"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
thiserror = "2.0"
anyhow = "1.0"

# UUID for session management
uuid = { version = "1.0", features = ["v4"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Configuration
toml = "0.8"

# Async traits
async-trait = "0.1"
