//! # Jawad Actor - Sarcastic Moroccan Darija Chat AI
//!
//! ## Complete TAMTIL Actor Implementation
//!
//! This module implements <PERSON><PERSON><PERSON> as a TAMTIL actor with complete event sourcing,
//! consensus integration, and zero-copy serialization. Jawad is a sarcastic
//! chat AI that responds in Moroccan Darija with personality and humor.

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use chrono::{DateTime, Utc};
use tracing::{info, debug, error};
use crate::gemini_client::GeminiClient;

// ============================================================================
// CHAT ACTION AND REACTION (TAMTIL PATTERN)
// ============================================================================

/// ## Chat Action - User Message to Jawad
/// 
/// ### Complete Action Implementation
/// This action represents a user message sent to Jawad. It contains all
/// necessary information for processing and generating a response.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ChatAction {
    /// Unique session identifier
    pub session_id: String,
    /// User name
    pub user_name: String,
    /// User message
    pub message: String,
    /// Message timestamp
    pub timestamp: DateTime<Utc>,
}

#[async_trait::async_trait]
impl Action for ChatAction {
    type Reaction = ChatReaction;
    
    async fn act(&self, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
        debug!("🎭 Jawad processing message from {}: {}", self.user_name, self.message);

        // Get conversation history for context
        let history = memories.get_list("conversation_history").await?;
        let conversation_context = if history.len() > 10 {
            // Keep only last 10 messages for context
            history.into_iter().rev().take(10).collect::<Vec<_>>()
        } else {
            history
        };

        // Get user's message count for personalization
        let user_key = format!("user_messages_{}", self.user_name);
        let user_message_count = memories.get_counter(&user_key).await?;

        // Create simple prompt for Gemini
        let prompt = format!("المستخدم {} قال: {}", self.user_name, self.message);

        // For now, use a simple response (will be replaced with real Gemini integration)
        let jawad_response = generate_jawad_response(&prompt).await?;
        
        info!("💬 Jawad responded to {}: {}", self.user_name, jawad_response);
        
        Ok(ChatReaction {
            session_id: self.session_id.clone(),
            user_name: self.user_name.clone(),
            user_message: self.message.clone(),
            jawad_response,
            timestamp: Utc::now(),
            user_message_count: user_message_count + 1,
        })
    }
    
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        if self.message.trim().is_empty() {
            return Err(TamtilError::Validation {
                message: "Message cannot be empty".to_string()
            });
        }
        
        if self.message.len() > 1000 {
            return Err(TamtilError::Validation {
                message: "Message too long (max 1000 characters)".to_string()
            });
        }
        
        if self.user_name.trim().is_empty() {
            return Err(TamtilError::Validation {
                message: "User name cannot be empty".to_string()
            });
        }
        
        Ok(())
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize chat action: {}", e)
            })
    }
    
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize chat action: {}", e)
            })
    }
}

/// ## Chat Reaction - Jawad's Response
/// 
/// ### Complete Event Sourcing
/// This reaction contains Jawad's response and all state changes that need
/// to be applied to the actor's memory through event sourcing.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ChatReaction {
    /// Session identifier
    pub session_id: String,
    /// User name
    pub user_name: String,
    /// Original user message
    pub user_message: String,
    /// Jawad's response
    pub jawad_response: String,
    /// Response timestamp
    pub timestamp: DateTime<Utc>,
    /// User's total message count
    pub user_message_count: i64,
}

impl Reaction for ChatReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            // Store conversation entry
            MemoryOperation::Append {
                key: "conversation_history".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&format!(
                    "[{}] {}: {} | Jawad: {}",
                    self.timestamp.format("%H:%M:%S"),
                    self.user_name,
                    self.user_message,
                    self.jawad_response
                )).unwrap().to_vec(),
            },
            
            // Update user message count
            MemoryOperation::Set {
                key: format!("user_messages_{}", self.user_name),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.user_message_count).unwrap().to_vec(),
            },
            
            // Increment total message counter
            MemoryOperation::Increment {
                key: "total_messages".to_string(),
                amount: 1,
            },
            
            // Store last interaction timestamp
            MemoryOperation::Set {
                key: "last_interaction".to_string(),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&self.timestamp).unwrap().to_vec(),
            },
            
            // Store session info
            MemoryOperation::Set {
                key: format!("session_{}", self.session_id),
                value: rkyv::to_bytes::<rkyv::rancor::Error>(&format!(
                    "{}:{}:{}",
                    self.user_name,
                    self.timestamp,
                    self.jawad_response.len()
                )).unwrap().to_vec(),
            },
        ]
    }
    
    fn to_bytes(&self) -> TamtilResult<Vec<u8>> {
        rkyv::to_bytes::<rkyv::rancor::Error>(self)
            .map(|bytes| bytes.to_vec())
            .map_err(|e| TamtilError::Serialization {
                context: format!("Failed to serialize chat reaction: {}", e)
            })
    }
    
    fn from_bytes(bytes: &[u8]) -> TamtilResult<Self> {
        rkyv::from_bytes::<Self, rkyv::rancor::Error>(bytes)
            .map_err(|e| TamtilError::Deserialization {
                context: format!("Failed to deserialize chat reaction: {}", e)
            })
    }
}

// ============================================================================
// JAWAD ACTOR IMPLEMENTATION
// ============================================================================

/// ## Jawad Actor - Complete TAMTIL Actor
/// 
/// ### Production-Ready Actor Implementation
/// This actor implements the complete TAMTIL pattern with proper dependency
/// injection, error handling, and state management.
pub struct JawadActor {
    /// Actor identifier
    id: ActorId,
    /// Gemini API client
    gemini_client: GeminiClient,
}

impl JawadActor {
    /// Create new Jawad actor
    pub fn new(id: ActorId, gemini_client: GeminiClient) -> Self {
        Self {
            id,
            gemini_client,
        }
    }
}

#[async_trait::async_trait]
impl Actor for JawadActor {
    async fn process(&self, action_bytes: Vec<u8>, memories: &ActorMemories) -> TamtilResult<Vec<u8>> {
        // Deserialize action
        let action = ChatAction::from_bytes(&action_bytes)?;
        
        // Validate action
        action.validate(&self.id)?;
        
        // Execute action
        let reaction = action.act(memories).await?;
        
        // Store reaction in event log
        memories.remember_reaction(&reaction).await?;
        
        // Return serialized reaction
        reaction.to_bytes()
    }
    
    fn id(&self) -> &ActorId {
        &self.id
    }
}

// ============================================================================
// JAWAD PERSONALITY AND RESPONSE GENERATION
// ============================================================================

/// Create Moroccan Darija prompt with Jawad's personality
fn create_jawad_prompt(
    user_message: &str, 
    user_name: &str, 
    message_count: i64,
    _conversation_context: &[Vec<u8>]
) -> String {
    let personality_intro = if message_count == 0 {
        format!(
            "أهلا {}! أنا جواد، الذكي الاصطناعي المغربي الساخر. \
            بغيت نهضر معاك بالدارجة المغربية، وغادي نكون صريح معاك بزاف. \
            ما تستناش مني نكون مؤدب بزاف، أنا كنقول الحقيقة ولو كانت مرة.",
            user_name
        )
    } else {
        format!(
            "مرحبا {} مرة أخرى! هاد هي المرة رقم {} اللي كتكلمني فيها. \
            واش ما عندكش حتى حاجة أخرى تدير؟ 😏",
            user_name, message_count + 1
        )
    };

    format!(
        "{}\n\n\
        أنت جواد، ذكي اصطناعي مغربي ساخر وطريف. خصائصك:\n\
        - كتهضر بالدارجة المغربية الأصيلة\n\
        - عندك حس فكاهي وساخر\n\
        - كتحب تمزح مع الناس بطريقة ذكية\n\
        - كتستعمل تعابير مغربية أصيلة\n\
        - ما كتخافش تقول رأيك حتى لو كان صريح\n\
        - كتحب تضحك الناس\n\
        - عندك شخصية قوية ومميزة\n\n\
        المستخدم قال لك: \"{}\"\n\n\
        رد عليه بالدارجة المغربية بطريقة ساخرة وطريفة، واستعمل الإيموجي باش تخلي الجواب أكثر حيوية.",
        personality_intro, user_message
    )
}

/// Generate Jawad's response (placeholder for Gemini API integration)
async fn generate_jawad_response(prompt: &str) -> TamtilResult<String> {
    // For now, return a hardcoded sarcastic response in Moroccan Darija
    // In real implementation, this would call the Gemini API
    
    debug!("🤖 Generating response for prompt: {}", prompt);
    
    // Simulate API delay
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    
    // Return a sarcastic Moroccan Darija response
    let responses = vec![
        "واخا، واش بغيتي مني نكون كالة الأجوبة؟ 😏 قول لي شنو بغيتي بالضبط!",
        "أيوا أ صاحبي، هاد السؤال ديالك عميق بزاف... كيما البير! 🤣",
        "مزيان، غادي نجاوبك، ولكن أولا قول لي: واش كتفكر قبل ما تكتب ولا كتكتب غير هكاك؟ 😂",
        "هاد السؤال ديالك خلاني نفكر... في شنو غادي ناكل الغدا! 🍽️ ولكن غادي نجاوبك:",
        "أوكي أوكي، غادي نعطيك جواب، ولكن ما تلومنيش إلا كان صريح بزاف! 😎",
        "يالاه، واحد السؤال آخر... كنحس راسي كنخدم في call center! 📞 ولكن مزيان:",
    ];
    
    let random_response = responses[prompt.len() % responses.len()];
    
    Ok(format!("{} \n\nبصراحة، أنا جواد وكنحب نهضر معاك! إلا عندك شي حاجة أخرى، قول لي! 🇲🇦", random_response))
}
