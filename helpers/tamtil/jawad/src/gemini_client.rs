//! # Gemini API Client for Jawad
//!
//! ## Production-Ready API Integration
//!
//! This module provides a complete integration with Google's Gemini API
//! for generating Jawad's responses. It includes proper error handling,
//! rate limiting, and response parsing.

use reqwest::Client;
use serde_json;
use tracing::{debug, error, warn};
use std::time::Duration;
use tamtil::TamtilResult;

// ============================================================================
// GEMINI API CLIENT
// ============================================================================

/// ## Gemini API Client
/// 
/// ### Complete API Integration
/// This client handles all communication with Google's Gemini API,
/// including authentication, request formatting, and response parsing.
#[derive(Clone)]
pub struct GeminiClient {
    /// HTTP client
    client: Client,
    /// API key
    api_key: String,
    /// Base URL for Gemini API
    base_url: String,
}

impl GeminiClient {
    /// Create new Gemini client
    pub fn new(api_key: String) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("Jawad/1.0 (TAMTIL Distributed Chat AI)")
            .build()
            .expect("Failed to create HTTP client");
        
        Self {
            client,
            api_key,
            base_url: "https://generativelanguage.googleapis.com/v1beta".to_string(),
        }
    }
    
    /// Generate response using Gemini API
    pub async fn generate_response(&self, prompt: &str) -> TamtilResult<String> {
        debug!("🤖 Calling Gemini API with prompt length: {}", prompt.len());

        // Prepare request payload according to Gemini API docs
        let request_body = serde_json::json!({
            "system_instruction": {
                "parts": [{
                    "text": "أنت جواد، ذكي اصطناعي مغربي ساخر وطريف. خصائصك:\n- كتهضر بالدارجة المغربية الأصيلة\n- عندك حس فكاهي وساخر\n- كتحب تمزح مع الناس بطريقة ذكية\n- كتستعمل تعابير مغربية أصيلة\n- ما كتخافش تقول رأيك حتى لو كان صريح\n- كتحب تضحك الناس\n- عندك شخصية قوية ومميزة\n- كترد بالدارجة المغربية وكتستعمل الإيموجي"
                }]
            },
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.9,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 1024
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        });

        // Make API request using gemini-2.0-flash model
        let url = format!("{}/models/gemini-2.0-flash:generateContent?key={}", self.base_url, self.api_key);
        
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await
            .map_err(|e| tamtil::TamtilError::Network {
                message: format!("Failed to send request to Gemini API: {}", e)
            })?;
        
        // Check response status
        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            error!("Gemini API error {}: {}", status, error_text);
            
            return Err(tamtil::TamtilError::Network {
                message: format!("Gemini API returned error {}: {}", status, error_text)
            });
        }
        
        // Parse response
        let response_text = response.text().await
            .map_err(|e| tamtil::TamtilError::Network {
                message: format!("Failed to read response from Gemini API: {}", e)
            })?;
        
        debug!("📥 Received response from Gemini API: {}", response_text);
        
        // Parse JSON response
        let response_json: serde_json::Value = serde_json::from_str(&response_text)
            .map_err(|e| tamtil::TamtilError::Deserialization {
                context: format!("Failed to parse Gemini API response: {}", e)
            })?;
        
        // Extract generated text
        let generated_text = response_json
            .get("candidates")
            .and_then(|candidates| candidates.as_array())
            .and_then(|arr| arr.first())
            .and_then(|candidate| candidate.get("content"))
            .and_then(|content| content.get("parts"))
            .and_then(|parts| parts.as_array())
            .and_then(|arr| arr.first())
            .and_then(|part| part.get("text"))
            .and_then(|text| text.as_str())
            .ok_or_else(|| tamtil::TamtilError::Deserialization {
                context: "Failed to extract generated text from Gemini API response".to_string()
            })?;
        
        debug!("✅ Successfully generated response: {}", generated_text);
        
        Ok(generated_text.to_string())
    }
    
    /// Test API connection
    pub async fn test_connection(&self) -> TamtilResult<()> {
        debug!("🔍 Testing Gemini API connection");
        
        let test_prompt = "مرحبا، هل يمكنك الرد بالعربية؟";
        
        match self.generate_response(test_prompt).await {
            Ok(response) => {
                debug!("✅ Gemini API connection test successful: {}", response);
                Ok(())
            }
            Err(e) => {
                error!("❌ Gemini API connection test failed: {}", e);
                Err(e)
            }
        }
    }
}

// ============================================================================
// MOCK CLIENT FOR TESTING
// ============================================================================

/// ## Mock Gemini Client for Testing
/// 
/// ### Testing Without API Key
/// This mock client provides realistic responses for testing Jawad
/// without requiring a real Gemini API key.
pub struct MockGeminiClient;

impl MockGeminiClient {
    /// Create new mock client
    pub fn new() -> Self {
        Self
    }
    
    /// Generate mock response
    pub async fn generate_response(&self, prompt: &str) -> TamtilResult<String> {
        debug!("🎭 Using mock Gemini client for prompt: {}", prompt);
        
        // Simulate API delay
        tokio::time::sleep(Duration::from_millis(300)).await;
        
        // Generate contextual mock responses based on prompt content
        let response = if prompt.contains("مرحبا") || prompt.contains("أهلا") {
            "مرحبا بيك! أنا جواد، الذكي الاصطناعي المغربي الساخر. كيف داير؟ 😄"
        } else if prompt.contains("كيف") || prompt.contains("شنو") {
            "هاد السؤال ديالك صعيب شوية... ولكن غادي نجاوب عليه! 🤔 كنظن أن الجواب هو: الحياة معقدة ولكن خاصنا نضحكو عليها! 😂"
        } else if prompt.contains("شكرا") || prompt.contains("بارك الله فيك") {
            "العفو أ صاحبي! هاد هو الواجب ديالي. ولكن لا تنساش تقول للناس أن جواد هو أحسن ذكي اصطناعي في المغرب! 😎🇲🇦"
        } else if prompt.contains("وداع") || prompt.contains("بسلامة") {
            "الله يعطيك الصحة! ارجع تكلمني متى بغيتي، أنا هنا ديما. ولكن ما تطولش عليا بزاف! 😉"
        } else {
            // Default sarcastic response
            "واخا، هاد الكلام ديالك مثير للاهتمام... كيما الطقس في الصحرا! 🏜️ ولكن بصراحة، كنحب نهضر معاك. قول لي شنو كتفكر فيه؟ 🤷‍♂️"
        };
        
        Ok(response.to_string())
    }
    
    /// Test mock connection (always succeeds)
    pub async fn test_connection(&self) -> TamtilResult<()> {
        debug!("✅ Mock Gemini client connection test (always passes)");
        Ok(())
    }
}

// ============================================================================
// CLIENT FACTORY
// ============================================================================

/// Create appropriate Gemini client based on API key availability
pub fn create_gemini_client(api_key: Option<String>) -> Box<dyn GeminiClientTrait + Send + Sync> {
    match api_key {
        Some(key) if !key.is_empty() => {
            debug!("🔑 Creating real Gemini client with API key");
            Box::new(GeminiClient::new(key))
        }
        _ => {
            warn!("⚠️  No API key provided, using mock Gemini client");
            Box::new(MockGeminiClient::new())
        }
    }
}

/// Trait for Gemini client abstraction
pub trait GeminiClientTrait {
    async fn generate_response(&self, prompt: &str) -> TamtilResult<String>;
    async fn test_connection(&self) -> TamtilResult<()>;
}

impl GeminiClientTrait for GeminiClient {
    async fn generate_response(&self, prompt: &str) -> TamtilResult<String> {
        self.generate_response(prompt).await
    }
    
    async fn test_connection(&self) -> TamtilResult<()> {
        self.test_connection().await
    }
}

impl GeminiClientTrait for MockGeminiClient {
    async fn generate_response(&self, prompt: &str) -> TamtilResult<String> {
        self.generate_response(prompt).await
    }
    
    async fn test_connection(&self) -> TamtilResult<()> {
        self.test_connection().await
    }
}
