//! # Jawad - Distributed Sarcastic Moroccan Darija Chat AI
//!
//! ## Real-World TAMTIL Demonstration
//!
//! Jawad is a distributed, fault-tolerant, sarcastic chat AI that speaks Moroccan Darija.
//! Built entirely on TAMTIL's actor system with embedded consensus, this demonstrates
//! how to build production-ready distributed applications with TAMTIL.
//!
//! ### Features
//! - **Distributed Architecture**: Multiple Jawad instances with consensus
//! - **Fault Tolerance**: Byzantine fault tolerance through TAMTIL
//! - **Event Sourcing**: Complete chat history and state management
//! - **Zero-Copy**: Pure rkyv serialization throughout
//! - **Real AI**: Integration with Google Gemini API
//! - **Moroccan Personality**: Sarcastic responses in Darija

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use clap::{Parser, Subcommand};
use tokio;
use tracing::{info, error, debug};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

mod jawad_actor;
mod gemini_client;
mod cli_client;
mod config;

use jawad_actor::*;
use gemini_client::*;
use cli_client::*;
use config::*;

// ============================================================================
// CLI INTERFACE
// ============================================================================

#[derive(Parser)]
#[command(name = "jawad")]
#[command(about = "Jawad - Distributed Sarcastic Moroccan Darija Chat AI")]
#[command(version = "1.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Start Jawad server node
    Server {
        /// Node ID (1, 2, or 3 for fault tolerance)
        #[arg(short, long, default_value = "1")]
        node_id: u64,

        /// Peer node IDs (comma-separated)
        #[arg(short, long, default_value = "2,3")]
        peers: String,

        /// Gemini API key
        #[arg(short, long, env = "GEMINI_API_KEY", default_value = "AIzaSyDyea4CzzDyhfzyjW7qrZz98Q08J5G-lj8")]
        api_key: String,

        /// Server port
        #[arg(long, default_value = "8080")]
        port: u16,
    },

    /// Start interactive chat client
    Chat {
        /// Server address
        #[arg(short, long, default_value = "127.0.0.1:8080")]
        server: String,

        /// User name
        #[arg(short, long, default_value = "Anonymous")]
        user: String,
    },

    /// Send single message to Jawad
    Message {
        /// Server address
        #[arg(short, long, default_value = "127.0.0.1:8080")]
        server: String,

        /// User name
        #[arg(short, long, default_value = "Anonymous")]
        user: String,

        /// Message to send
        message: String,
    },
}

// ============================================================================
// MAIN APPLICATION
// ============================================================================

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("jawad=debug,tamtil=info")
        .init();

    let cli = Cli::parse();

    match cli.command {
        Commands::Server { node_id, peers, api_key, port } => {
            start_server(node_id, peers, api_key, port).await?;
        }
        Commands::Chat { server, user } => {
            start_chat_client(server, user).await?;
        }
        Commands::Message { server, user, message } => {
            send_single_message(server, user, message).await?;
        }
    }

    Ok(())
}

/// Start Jawad server with distributed consensus
async fn start_server(node_id: u64, peers_str: String, api_key: String, port: u16) -> anyhow::Result<()> {
    info!("🚀 Starting Jawad server node {} on port {}", node_id, port);

    // Parse peer node IDs
    let peers: Vec<u64> = peers_str
        .split(',')
        .filter_map(|s| s.trim().parse().ok())
        .filter(|&id| id != node_id)
        .collect();

    info!("👥 Connecting to peer nodes: {:?}", peers);

    // Create TAMTIL platform with fault tolerance
    let platform = Platform::new(
        format!("jawad_platform_{}", node_id),
        node_id,
        peers
    );

    // Create Jawad context
    platform.create_context("jawad_context").await
        .map_err(|e| anyhow::anyhow!("Failed to create context: {}", e))?;

    let context_id = ActorId::new("jawad_context");

    // Create Gemini client
    let gemini_client = GeminiClient::new(api_key);

    // Create Jawad actor
    let jawad_actor = JawadActor::new(
        ActorId::new("jawad"),
        gemini_client
    );

    platform.add_actor_to_context(&context_id, jawad_actor).await
        .map_err(|e| anyhow::anyhow!("Failed to add Jawad actor: {}", e))?;

    info!("✅ Jawad actor spawned and ready");

    // Start HTTP server for client connections
    start_http_server(platform, context_id, port).await?;

    Ok(())
}

/// Start HTTP server for client connections
async fn start_http_server(
    platform: Platform,
    context_id: ActorId,
    port: u16
) -> anyhow::Result<()> {
    use std::sync::Arc;
    use tokio::net::TcpListener;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};

    let platform = Arc::new(platform);
    let context_id = Arc::new(context_id);

    let listener = TcpListener::bind(format!("0.0.0.0:{}", port)).await?;
    info!("🌐 HTTP server listening on port {}", port);

    loop {
        let (mut socket, addr) = listener.accept().await?;
        let platform = Arc::clone(&platform);
        let context_id = Arc::clone(&context_id);

        tokio::spawn(async move {
            let mut buffer = [0; 4096];

            match socket.read(&mut buffer).await {
                Ok(n) if n > 0 => {
                    let request = String::from_utf8_lossy(&buffer[..n]);
                    debug!("📨 Received request from {}: {}", addr, request.trim());

                    // Parse simple HTTP-like request
                    if let Some(message_line) = request.lines().find(|line| line.starts_with("Message:")) {
                        let message = message_line.strip_prefix("Message:").unwrap_or("").trim();
                        let user = request.lines()
                            .find(|line| line.starts_with("User:"))
                            .and_then(|line| line.strip_prefix("User:"))
                            .unwrap_or("Anonymous")
                            .trim();

                        // Create chat action
                        let chat_action = ChatAction {
                            session_id: Uuid::new_v4().to_string(),
                            user_name: user.to_string(),
                            message: message.to_string(),
                            timestamp: Utc::now(),
                        };

                        // Send to Jawad actor through TAMTIL
                        match chat_action.to_bytes() {
                            Ok(action_bytes) => {
                                match platform.send_action(
                                    &context_id,
                                    &ActorId::new("jawad"),
                                    action_bytes
                                ).await {
                                    Ok(reaction_bytes) => {
                                        match ChatReaction::from_bytes(&reaction_bytes) {
                                            Ok(reaction) => {
                                                let response = format!(
                                                    "HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=utf-8\r\n\r\n{}\r\n",
                                                    reaction.jawad_response
                                                );
                                                let _ = socket.write_all(response.as_bytes()).await;
                                            }
                                            Err(e) => {
                                                error!("Failed to deserialize reaction: {}", e);
                                                let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nDeserialization error\r\n";
                                                let _ = socket.write_all(response.as_bytes()).await;
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        error!("Failed to send action to Jawad: {}", e);
                                        let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nActor error\r\n";
                                        let _ = socket.write_all(response.as_bytes()).await;
                                    }
                                }
                            }
                            Err(e) => {
                                error!("Failed to serialize action: {}", e);
                                let response = "HTTP/1.1 500 Internal Server Error\r\n\r\nSerialization error\r\n";
                                let _ = socket.write_all(response.as_bytes()).await;
                            }
                        }
                    } else {
                        let response = "HTTP/1.1 400 Bad Request\r\n\r\nInvalid request format\r\n";
                        let _ = socket.write_all(response.as_bytes()).await;
                    }
                }
                Ok(_) => {
                    debug!("Empty request from {}", addr);
                }
                Err(e) => {
                    error!("Failed to read from socket {}: {}", addr, e);
                }
            }
        });
    }
}

/// Start interactive chat client
async fn start_chat_client(server: String, user: String) -> anyhow::Result<()> {
    let client = ChatClient::new(server, user);
    client.start_interactive().await
}

/// Send single message and exit
async fn send_single_message(server: String, user: String, message: String) -> anyhow::Result<()> {
    let client = ChatClient::new(server, user);
    let response = client.send_message(message).await?;
    println!("{}", response);
    Ok(())
}
